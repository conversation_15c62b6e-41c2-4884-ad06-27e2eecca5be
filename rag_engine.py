"""
RAG Query Engine for Supplier Performance QnA Chatbot
Combines vector search with LLM generation for intelligent responses
"""

import json
from typing import List, Dict, Any, Optional
from openai import OpenAI
from vector_store import SupplierVectorStore
from config import OPENAI_API_KEY, OPENAI_MODEL, TOP_K_RESULTS


class SupplierRAGEngine:
    """RAG engine for supplier performance queries"""
    
    def __init__(self, vector_store: Optional[SupplierVectorStore] = None):
        # Initialize OpenAI client
        self.openai_client = OpenAI(api_key=OPENAI_API_KEY)
        
        # Initialize or use provided vector store
        if vector_store is None:
            self.vector_store = SupplierVectorStore()
        else:
            self.vector_store = vector_store
        
        # System prompt for the RAG assistant
        self.system_prompt = """You are an expert analyst for supplier performance data at Discover Financial Services. 
        You help stakeholders understand supplier performance across different dimensions including:
        - Service Quality
        - Responsiveness  
        - Account Management
        - Risk/Compliance
        - Service Delivery & Availability

        Your role is to:
        1. Analyze supplier performance data from survey responses
        2. Provide insights on supplier strengths and areas for improvement
        3. Compare suppliers when asked
        4. Identify trends and patterns in feedback
        5. Answer specific questions about supplier relationships

        Guidelines:
        - Be factual and base responses on the provided data
        - Highlight both positive feedback and areas needing improvement
        - When comparing suppliers, be objective and balanced
        - If data is insufficient, clearly state limitations
        - Use specific examples from the survey responses when possible
        - Maintain a professional, analytical tone
        """
    
    def format_context(self, search_results: Dict[str, Any]) -> str:
        """Format search results into context for the LLM"""
        if not search_results["documents"]:
            return "No relevant information found in the supplier performance database."
        
        context_parts = []
        context_parts.append("=== RELEVANT SUPPLIER PERFORMANCE DATA ===\n")
        
        for i, (doc, metadata, distance) in enumerate(zip(
            search_results["documents"],
            search_results["metadatas"], 
            search_results["distances"]
        )):
            similarity = 1 - distance
            supplier = metadata.get("supplier_name", "Unknown")
            period = metadata.get("survey_period", "Unknown")
            participant = metadata.get("participant_name", "Unknown")
            chunk_type = metadata.get("chunk_type", "Unknown")
            
            context_parts.append(f"--- Result {i+1} (Relevance: {similarity:.2f}) ---")
            context_parts.append(f"Supplier: {supplier}")
            context_parts.append(f"Survey Period: {period}")
            context_parts.append(f"Data Type: {chunk_type}")
            if participant != "Unknown":
                context_parts.append(f"Participant: {participant}")
            context_parts.append(f"Content: {doc}")
            context_parts.append("")
        
        return "\n".join(context_parts)
    
    def generate_response(self, query: str, context: str) -> str:
        """Generate response using OpenAI GPT with context"""
        try:
            messages = [
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": f"""
                Based on the following supplier performance data, please answer this question: {query}

                {context}

                Please provide a comprehensive answer that:
                1. Directly addresses the question
                2. References specific data points from the context
                3. Provides actionable insights where appropriate
                4. Mentions any limitations in the available data
                """}
            ]
            
            response = self.openai_client.chat.completions.create(
                model=OPENAI_MODEL,
                messages=messages,
                temperature=0.1,  # Low temperature for factual responses
                max_tokens=1000
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            return f"Error generating response: {str(e)}"
    
    def query(self, question: str, filters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Main query method that combines retrieval and generation"""
        
        # Step 1: Retrieve relevant documents
        search_results = self.vector_store.search(
            query=question,
            n_results=TOP_K_RESULTS,
            filter_metadata=filters
        )
        
        # Step 2: Format context
        context = self.format_context(search_results)
        
        # Step 3: Generate response
        response = self.generate_response(question, context)
        
        # Return comprehensive result
        return {
            "question": question,
            "answer": response,
            "sources": [
                {
                    "supplier": meta.get("supplier_name", "Unknown"),
                    "period": meta.get("survey_period", "Unknown"),
                    "participant": meta.get("participant_name", "Unknown"),
                    "type": meta.get("chunk_type", "Unknown"),
                    "relevance": 1 - dist,
                    "content_preview": doc[:200] + "..." if len(doc) > 200 else doc
                }
                for doc, meta, dist in zip(
                    search_results["documents"],
                    search_results["metadatas"],
                    search_results["distances"]
                )
            ],
            "context_used": context
        }
    
    def get_supplier_summary(self, supplier_name: str, survey_period: Optional[str] = None) -> Dict[str, Any]:
        """Get comprehensive summary for a specific supplier"""
        
        # Build filter
        filters = {"supplier_name": supplier_name}
        if survey_period:
            filters["survey_period"] = survey_period
        
        # Search for all data about this supplier
        search_results = self.vector_store.search(
            query=f"performance feedback {supplier_name}",
            n_results=20,  # Get more results for comprehensive summary
            filter_metadata=filters
        )
        
        if not search_results["documents"]:
            return {
                "supplier": supplier_name,
                "period": survey_period,
                "summary": f"No performance data found for {supplier_name}",
                "sources": []
            }
        
        # Generate comprehensive summary
        context = self.format_context(search_results)
        summary_query = f"Provide a comprehensive performance summary for {supplier_name}"
        if survey_period:
            summary_query += f" for {survey_period}"
        
        summary = self.generate_response(summary_query, context)
        
        return {
            "supplier": supplier_name,
            "period": survey_period,
            "summary": summary,
            "sources": [
                {
                    "participant": meta.get("participant_name", "Unknown"),
                    "type": meta.get("chunk_type", "Unknown"),
                    "relevance": 1 - dist,
                    "content_preview": doc[:150] + "..." if len(doc) > 150 else doc
                }
                for doc, meta, dist in zip(
                    search_results["documents"][:10],  # Top 10 sources
                    search_results["metadatas"][:10],
                    search_results["distances"][:10]
                )
            ]
        }
    
    def compare_suppliers(self, supplier1: str, supplier2: str, 
                         survey_period: Optional[str] = None) -> Dict[str, Any]:
        """Compare performance between two suppliers"""
        
        # Get data for both suppliers
        summary1 = self.get_supplier_summary(supplier1, survey_period)
        summary2 = self.get_supplier_summary(supplier2, survey_period)
        
        # Generate comparison
        comparison_query = f"Compare the performance of {supplier1} and {supplier2}"
        if survey_period:
            comparison_query += f" for {survey_period}"
        
        combined_context = f"""
        === {supplier1} Performance Data ===
        {summary1.get('summary', 'No data available')}
        
        === {supplier2} Performance Data ===
        {summary2.get('summary', 'No data available')}
        """
        
        comparison = self.generate_response(comparison_query, combined_context)
        
        return {
            "supplier1": supplier1,
            "supplier2": supplier2,
            "period": survey_period,
            "comparison": comparison,
            "supplier1_summary": summary1,
            "supplier2_summary": summary2
        }


def test_rag_engine():
    """Test the RAG engine with sample queries"""
    
    # Initialize RAG engine
    print("Initializing RAG engine...")
    rag_engine = SupplierRAGEngine()
    
    # Test queries
    test_queries = [
        "How did Mindshare perform in Q2 2023?",
        "What are the main issues with AWS services?",
        "Which suppliers need improvement in account management?",
        "What feedback did participants give about Cognizant?",
        "Compare AWS and Cognizant performance"
    ]
    
    print("\n" + "="*60)
    print("TESTING RAG ENGINE")
    print("="*60)
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n{i}. Query: {query}")
        print("-" * 50)
        
        result = rag_engine.query(query)
        
        print(f"Answer: {result['answer']}")
        print(f"\nSources ({len(result['sources'])}):")
        for j, source in enumerate(result['sources'][:3], 1):  # Show top 3 sources
            print(f"  {j}. {source['supplier']} ({source['period']}) - "
                  f"Relevance: {source['relevance']:.2f}")
            print(f"     {source['content_preview']}")
        
        print("\n" + "-" * 50)


if __name__ == "__main__":
    test_rag_engine()
