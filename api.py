"""
FastAPI application for Supplier Performance QnA Chatbot
Provides REST API endpoints for querying supplier performance data
"""

from fastapi import FastAP<PERSON>, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, <PERSON>
from typing import List, Dict, Any, Optional
import uvicorn
from rag_engine import SupplierRAGEngine
from vector_store import SupplierVectorStore
import json

# Initialize FastAPI app
app = FastAPI(
    title="Supplier Performance QnA API",
    description="API for querying supplier performance survey data using RAG (Retrieval-Augmented Generation)",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize RAG engine globally
rag_engine = None

# Pydantic models for request/response
class QueryRequest(BaseModel):
    question: str = Field(..., description="The question to ask about supplier performance", example="How did Mindshare perform in Q2 2023?")
    filters: Optional[Dict[str, str]] = Field(None, description="Optional filters for supplier, period, etc.", example={"supplier_name": "Mindshare", "survey_period": "Q2 2023"})

class QueryResponse(BaseModel):
    question: str
    answer: str
    sources: List[Dict[str, Any]]
    context_used: str

class SupplierSummaryRequest(BaseModel):
    supplier_name: str = Field(..., description="Name of the supplier", example="Mindshare - Consumer Banking")
    survey_period: Optional[str] = Field(None, description="Survey period (optional)", example="Q2 2023")

class SupplierSummaryResponse(BaseModel):
    supplier: str
    period: Optional[str]
    summary: str
    sources: List[Dict[str, Any]]

class ComparisonRequest(BaseModel):
    supplier1: str = Field(..., description="First supplier to compare", example="AWS - Business Technology")
    supplier2: str = Field(..., description="Second supplier to compare", example="Cognizant - Business Technology")
    survey_period: Optional[str] = Field(None, description="Survey period (optional)", example="Q2 2023")

class ComparisonResponse(BaseModel):
    supplier1: str
    supplier2: str
    period: Optional[str]
    comparison: str
    supplier1_summary: Dict[str, Any]
    supplier2_summary: Dict[str, Any]

class StatsResponse(BaseModel):
    total_chunks: int
    unique_suppliers: int
    chunk_types: List[str]
    survey_periods: List[str]
    sample_suppliers: List[str]

# Startup event to initialize RAG engine
@app.on_event("startup")
async def startup_event():
    global rag_engine
    try:
        print("Initializing RAG engine...")
        rag_engine = SupplierRAGEngine()
        print("RAG engine initialized successfully!")
    except Exception as e:
        print(f"Error initializing RAG engine: {e}")
        raise e

# Health check endpoint
@app.get("/health", tags=["Health"])
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "message": "Supplier Performance QnA API is running"}

# Main query endpoint
@app.post("/query", response_model=QueryResponse, tags=["Query"])
async def query_supplier_data(request: QueryRequest):
    """
    Query supplier performance data using natural language.
    
    This endpoint uses RAG (Retrieval-Augmented Generation) to find relevant 
    supplier performance data and generate comprehensive answers.
    """
    if rag_engine is None:
        raise HTTPException(status_code=500, detail="RAG engine not initialized")
    
    try:
        result = rag_engine.query(request.question, request.filters)
        return QueryResponse(**result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing query: {str(e)}")

# Supplier summary endpoint
@app.post("/supplier-summary", response_model=SupplierSummaryResponse, tags=["Analysis"])
async def get_supplier_summary(request: SupplierSummaryRequest):
    """
    Get a comprehensive performance summary for a specific supplier.
    
    This endpoint provides detailed analysis of a supplier's performance
    across all available survey data.
    """
    if rag_engine is None:
        raise HTTPException(status_code=500, detail="RAG engine not initialized")
    
    try:
        result = rag_engine.get_supplier_summary(request.supplier_name, request.survey_period)
        return SupplierSummaryResponse(**result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating supplier summary: {str(e)}")

# Supplier comparison endpoint
@app.post("/compare-suppliers", response_model=ComparisonResponse, tags=["Analysis"])
async def compare_suppliers(request: ComparisonRequest):
    """
    Compare performance between two suppliers.
    
    This endpoint provides a detailed comparison of two suppliers' performance
    across various dimensions like service quality, responsiveness, etc.
    """
    if rag_engine is None:
        raise HTTPException(status_code=500, detail="RAG engine not initialized")
    
    try:
        result = rag_engine.compare_suppliers(request.supplier1, request.supplier2, request.survey_period)
        return ComparisonResponse(**result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error comparing suppliers: {str(e)}")

# Get available suppliers
@app.get("/suppliers", tags=["Metadata"])
async def get_suppliers():
    """Get list of available suppliers in the database"""
    if rag_engine is None:
        raise HTTPException(status_code=500, detail="RAG engine not initialized")
    
    try:
        stats = rag_engine.vector_store.get_collection_stats()
        return {
            "suppliers": stats.get("sample_suppliers", []),
            "total_suppliers": stats.get("unique_suppliers", 0)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting suppliers: {str(e)}")

# Get database statistics
@app.get("/stats", response_model=StatsResponse, tags=["Metadata"])
async def get_database_stats():
    """Get statistics about the supplier performance database"""
    if rag_engine is None:
        raise HTTPException(status_code=500, detail="RAG engine not initialized")
    
    try:
        stats = rag_engine.vector_store.get_collection_stats()
        return StatsResponse(**stats)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting database stats: {str(e)}")

# Search endpoint with pagination
@app.get("/search", tags=["Search"])
async def search_supplier_data(
    query: str = Query(..., description="Search query", example="account management issues"),
    limit: int = Query(5, ge=1, le=20, description="Number of results to return"),
    supplier_filter: Optional[str] = Query(None, description="Filter by supplier name"),
    period_filter: Optional[str] = Query(None, description="Filter by survey period")
):
    """
    Search supplier performance data with optional filters.
    
    This endpoint provides direct access to the vector search functionality
    without LLM generation, useful for exploring raw data.
    """
    if rag_engine is None:
        raise HTTPException(status_code=500, detail="RAG engine not initialized")
    
    try:
        # Build filters
        filters = {}
        if supplier_filter:
            filters["supplier_name"] = supplier_filter
        if period_filter:
            filters["survey_period"] = period_filter
        
        # Search
        results = rag_engine.vector_store.search(
            query=query,
            n_results=limit,
            filter_metadata=filters if filters else None
        )
        
        # Format results
        search_results = []
        for doc, metadata, distance in zip(
            results["documents"],
            results["metadatas"],
            results["distances"]
        ):
            search_results.append({
                "content": doc,
                "metadata": metadata,
                "relevance_score": 1 - distance,
                "supplier": metadata.get("supplier_name", "Unknown"),
                "period": metadata.get("survey_period", "Unknown"),
                "type": metadata.get("chunk_type", "Unknown")
            })
        
        return {
            "query": query,
            "results": search_results,
            "total_results": len(search_results),
            "filters_applied": filters
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error searching data: {str(e)}")

# Example queries endpoint
@app.get("/examples", tags=["Help"])
async def get_example_queries():
    """Get example queries to help users understand the API capabilities"""
    return {
        "example_queries": [
            "How did Mindshare perform in Q2 2023?",
            "What are the main issues with AWS services?",
            "Which suppliers need improvement in account management?",
            "What feedback did participants give about Cognizant?",
            "Compare AWS and Cognizant performance",
            "Show me all feedback about service quality",
            "Which suppliers have the best responsiveness ratings?",
            "What are the common themes in participant feedback?",
            "How has supplier performance changed over time?",
            "What projects has Mindshare supported?"
        ],
        "example_filters": {
            "supplier_name": "Mindshare - Consumer Banking",
            "survey_period": "Q2 2023"
        },
        "available_suppliers": [
            "Mindshare - Consumer Banking",
            "AWS - Business Technology", 
            "Cognizant - Business Technology",
            "Equifax - Consumer Banking"
        ]
    }

if __name__ == "__main__":
    uvicorn.run(
        "api:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
