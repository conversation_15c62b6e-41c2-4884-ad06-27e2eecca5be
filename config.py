"""
Configuration settings for the Supplier Performance QnA Chatbot
"""
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# OpenAI Configuration
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
OPENAI_MODEL = "gpt-4"
EMBEDDING_MODEL = "text-embedding-ada-002"

# Vector Database Configuration
CHROMA_PERSIST_DIRECTORY = "./chroma_db"
COLLECTION_NAME = "supplier_performance"

# Data Processing Configuration
CHUNK_SIZE = 1000
CHUNK_OVERLAP = 200
MAX_TOKENS_PER_CHUNK = 8000

# Retrieval Configuration
TOP_K_RESULTS = 5
SIMILARITY_THRESHOLD = 0.7

# Streamlit Configuration
PAGE_TITLE = "Supplier Performance QnA Chatbot"
PAGE_ICON = "🤖"

# Sample data files
SAMPLE_FILES = [
    "sample1.json",
    "sample2.json"
]
