"""
Vector Database Setup for Supplier Performance QnA Chatbot
Uses ChromaDB for vector storage and OpenAI embeddings
"""

import json
import os
from typing import List, Dict, Any, Optional
import chromadb
from chromadb.config import Settings
import openai
from openai import OpenAI
import tiktoken
from config import (
    OPENAI_API_KEY, EMBEDDING_MODEL, CHROMA_PERSIST_DIRECTORY, 
    COLLECTION_NAME, MAX_TOKENS_PER_CHUNK
)


class SupplierVectorStore:
    """Vector store for supplier performance survey data"""
    
    def __init__(self):
        # Initialize OpenAI client
        self.openai_client = OpenAI(api_key=OPENAI_API_KEY)
        
        # Initialize ChromaDB
        self.chroma_client = chromadb.PersistentClient(
            path=CHROMA_PERSIST_DIRECTORY,
            settings=Settings(anonymized_telemetry=False)
        )
        
        # Get or create collection
        self.collection = self.chroma_client.get_or_create_collection(
            name=COLLECTION_NAME,
            metadata={"description": "Supplier performance survey data"}
        )
        
        # Initialize tokenizer for token counting
        self.tokenizer = tiktoken.encoding_for_model("gpt-4")
        
        print(f"Vector store initialized with collection: {COLLECTION_NAME}")
        print(f"Current collection size: {self.collection.count()}")
    
    def count_tokens(self, text: str) -> int:
        """Count tokens in text"""
        return len(self.tokenizer.encode(text))
    
    def truncate_text(self, text: str, max_tokens: int = MAX_TOKENS_PER_CHUNK) -> str:
        """Truncate text to fit within token limit"""
        tokens = self.tokenizer.encode(text)
        if len(tokens) <= max_tokens:
            return text
        
        # Truncate and decode back to text
        truncated_tokens = tokens[:max_tokens]
        return self.tokenizer.decode(truncated_tokens)
    
    def generate_embedding(self, text: str) -> List[float]:
        """Generate embedding for text using OpenAI"""
        try:
            # Truncate text if too long
            text = self.truncate_text(text)
            
            response = self.openai_client.embeddings.create(
                model=EMBEDDING_MODEL,
                input=text
            )
            return response.data[0].embedding
        except Exception as e:
            print(f"Error generating embedding: {e}")
            return []
    
    def add_chunks(self, chunks: List[Dict[str, Any]]) -> bool:
        """Add chunks to vector store"""
        try:
            documents = []
            metadatas = []
            ids = []
            embeddings = []
            
            print(f"Processing {len(chunks)} chunks for embedding...")
            
            for i, chunk in enumerate(chunks):
                chunk_id = chunk.get('chunk_id', f'chunk_{i}')
                content = chunk.get('content', '')
                metadata = chunk.get('metadata', {})
                
                # Skip empty content
                if not content.strip():
                    continue
                
                # Generate embedding
                embedding = self.generate_embedding(content)
                if not embedding:
                    print(f"Skipping chunk {chunk_id} - failed to generate embedding")
                    continue
                
                documents.append(content)
                metadatas.append(metadata)
                ids.append(chunk_id)
                embeddings.append(embedding)
                
                if (i + 1) % 50 == 0:
                    print(f"Processed {i + 1}/{len(chunks)} chunks")
            
            # Add to collection
            if documents:
                self.collection.add(
                    documents=documents,
                    metadatas=metadatas,
                    ids=ids,
                    embeddings=embeddings
                )
                print(f"Successfully added {len(documents)} chunks to vector store")
                return True
            else:
                print("No valid chunks to add")
                return False
                
        except Exception as e:
            print(f"Error adding chunks to vector store: {e}")
            return False
    
    def search(self, query: str, n_results: int = 5, 
               filter_metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Search for similar chunks"""
        try:
            # Generate query embedding
            query_embedding = self.generate_embedding(query)
            if not query_embedding:
                return {"documents": [], "metadatas": [], "distances": []}
            
            # Search in collection
            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=n_results,
                where=filter_metadata
            )
            
            return {
                "documents": results["documents"][0] if results["documents"] else [],
                "metadatas": results["metadatas"][0] if results["metadatas"] else [],
                "distances": results["distances"][0] if results["distances"] else [],
                "ids": results["ids"][0] if results["ids"] else []
            }
            
        except Exception as e:
            print(f"Error searching vector store: {e}")
            return {"documents": [], "metadatas": [], "distances": []}
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """Get statistics about the collection"""
        try:
            count = self.collection.count()
            
            # Get sample of metadata to understand data distribution
            if count > 0:
                sample_results = self.collection.get(limit=min(100, count))
                
                # Analyze metadata
                suppliers = set()
                chunk_types = set()
                survey_periods = set()
                
                for metadata in sample_results.get("metadatas", []):
                    if metadata:
                        suppliers.add(metadata.get("supplier_name", "Unknown"))
                        chunk_types.add(metadata.get("chunk_type", "Unknown"))
                        survey_periods.add(metadata.get("survey_period", "Unknown"))
                
                return {
                    "total_chunks": count,
                    "unique_suppliers": len(suppliers),
                    "chunk_types": list(chunk_types),
                    "survey_periods": list(survey_periods),
                    "sample_suppliers": list(suppliers)[:10]  # First 10 suppliers
                }
            else:
                return {"total_chunks": 0}
                
        except Exception as e:
            print(f"Error getting collection stats: {e}")
            return {"error": str(e)}
    
    def clear_collection(self):
        """Clear all data from collection"""
        try:
            # Delete the collection and recreate it
            self.chroma_client.delete_collection(name=COLLECTION_NAME)
            self.collection = self.chroma_client.get_or_create_collection(
                name=COLLECTION_NAME,
                metadata={"description": "Supplier performance survey data"}
            )
            print("Collection cleared successfully")
        except Exception as e:
            print(f"Error clearing collection: {e}")


def load_and_index_data(chunks_file: str = "processed_chunks.json") -> SupplierVectorStore:
    """Load processed chunks and index them in vector store"""
    
    # Initialize vector store
    vector_store = SupplierVectorStore()
    
    # Load chunks
    try:
        with open(chunks_file, 'r', encoding='utf-8') as f:
            chunks = json.load(f)
        print(f"Loaded {len(chunks)} chunks from {chunks_file}")
    except Exception as e:
        print(f"Error loading chunks file: {e}")
        return vector_store
    
    # Clear existing data (optional - comment out to append)
    # vector_store.clear_collection()
    
    # Add chunks to vector store
    success = vector_store.add_chunks(chunks)
    
    if success:
        # Print statistics
        stats = vector_store.get_collection_stats()
        print("\nVector Store Statistics:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
    
    return vector_store


if __name__ == "__main__":
    # Load and index the processed data (test with first 100 chunks)
    vector_store = SupplierVectorStore()

    # Load chunks
    try:
        with open("processed_chunks.json", 'r', encoding='utf-8') as f:
            chunks = json.load(f)
        print(f"Loaded {len(chunks)} chunks from processed_chunks.json")

        # Test with first 100 chunks to avoid rate limits
        test_chunks = chunks[:100]
        print(f"Testing with first {len(test_chunks)} chunks")

        success = vector_store.add_chunks(test_chunks)
        if success:
            stats = vector_store.get_collection_stats()
            print("\nVector Store Statistics:")
            for key, value in stats.items():
                print(f"  {key}: {value}")

    except Exception as e:
        print(f"Error: {e}")
        exit(1)
    
    # Test search functionality
    test_queries = [
        "How did Mindshare perform in Q2 2023?",
        "What are the issues with AWS services?",
        "Which suppliers need improvement in account management?",
        "Show me feedback about Cognizant performance"
    ]
    
    print("\n" + "="*50)
    print("Testing Search Functionality")
    print("="*50)
    
    for query in test_queries:
        print(f"\nQuery: {query}")
        print("-" * 40)
        
        results = vector_store.search(query, n_results=3)
        
        for i, (doc, metadata, distance) in enumerate(zip(
            results["documents"], 
            results["metadatas"], 
            results["distances"]
        )):
            print(f"\nResult {i+1} (similarity: {1-distance:.3f}):")
            print(f"Supplier: {metadata.get('supplier_name', 'Unknown')}")
            print(f"Period: {metadata.get('survey_period', 'Unknown')}")
            print(f"Type: {metadata.get('chunk_type', 'Unknown')}")
            print(f"Content: {doc[:200]}...")
        
        print("\n" + "-" * 40)
