"""
Data Processing Pipeline for Supplier Performance Survey Data
Extracts and flattens JSON data into meaningful chunks for RAG embedding
"""

import json
import pandas as pd
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import re
from datetime import datetime


@dataclass
class SurveyChunk:
    """Represents a meaningful chunk of survey data for embedding"""
    chunk_id: str
    content: str
    metadata: Dict[str, Any]
    chunk_type: str  # 'supplier_overview', 'survey_response', 'participant_feedback', etc.


class SupplierDataProcessor:
    """Processes supplier performance survey JSON data into chunks for RAG"""

    def __init__(self):
        self.chunks: List[SurveyChunk] = []
        self.chunk_counter = 0  # Counter to ensure unique IDs
        
    def load_json_file(self, file_path: str) -> Dict[str, Any]:
        """Load JSON data from file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error loading {file_path}: {e}")
            return {}
    
    def clean_text(self, text: str) -> str:
        """Clean and normalize text content"""
        if not text or text == "N/A" or text == "n/a" or text == "na":
            return ""
        
        # Remove extra whitespace and normalize
        text = re.sub(r'\s+', ' ', text.strip())
        # Remove unicode characters that might cause issues
        text = text.encode('ascii', 'ignore').decode('ascii')
        return text
    
    def extract_supplier_overview(self, supplier_data: Dict[str, Any], 
                                project_name: str, survey_period: str) -> List[SurveyChunk]:
        """Extract supplier overview information"""
        chunks = []
        
        supplier_name = supplier_data.get('supplierName', 'Unknown')
        
        # Create supplier overview chunk
        overview_content = f"""
        Supplier: {supplier_name}
        Project: {project_name}
        Survey Period: {survey_period}
        
        This supplier is being evaluated for performance across multiple dimensions including:
        - Service Quality
        - Responsiveness  
        - Account Management
        - Risk Management
        - Overall Performance
        """
        
        chunk = SurveyChunk(
            chunk_id=f"{supplier_name}_{survey_period}_overview",
            content=self.clean_text(overview_content),
            metadata={
                'supplier_name': supplier_name,
                'project_name': project_name,
                'survey_period': survey_period,
                'chunk_type': 'supplier_overview'
            },
            chunk_type='supplier_overview'
        )
        chunks.append(chunk)
        
        return chunks

    def extract_supplier_overview_with_attributes(self, supplier_data: Dict[str, Any],
                                                 project_name: str, survey_period: str) -> List[SurveyChunk]:
        """Extract supplier overview with attribute values"""
        chunks = []

        supplier_name = supplier_data.get('name', 'Unknown')
        attribute_values = supplier_data.get('attributeValues', [])

        # Create supplier overview with attributes
        overview_content = f"""
        Supplier: {supplier_name}
        Project: {project_name}
        Survey Period: {survey_period}

        Performance Attributes:
        """

        for attr in attribute_values:
            attr_title = attr.get('attributeTitle', 'Unknown Attribute')
            value = attr.get('value', 'N/A')
            score_type = attr.get('scoreType', 'unknown')
            overview_content += f"\n- {attr_title}: {value} ({score_type})"

        # Create unique chunk ID with counter
        self.chunk_counter += 1
        chunk_id = f"chunk_{self.chunk_counter}_{supplier_name}_{survey_period}_overview"

        chunk = SurveyChunk(
            chunk_id=chunk_id,
            content=self.clean_text(overview_content),
            metadata={
                'supplier_name': supplier_name,
                'project_name': project_name,
                'survey_period': survey_period,
                'chunk_type': 'supplier_overview',
                'has_attributes': len(attribute_values) > 0
            },
            chunk_type='supplier_overview'
        )
        chunks.append(chunk)

        return chunks

    def extract_participant_responses(self, participant_data: Dict[str, Any],
                                    supplier_name: str, project_name: str,
                                    survey_period: str) -> List[SurveyChunk]:
        """Extract individual participant survey responses"""
        chunks = []

        participant_name = participant_data.get('name', 'Anonymous')
        survey_results = participant_data.get('surveyResults', [])

        if not survey_results:
            return chunks
            
        # Group responses by category for better chunking
        response_groups = {
            'performance_ratings': [],
            'text_feedback': [],
            'business_context': [],
            'projects_initiatives': []
        }

        # Process all survey results for this participant
        for survey_result in survey_results:
            responses = survey_result.get('responses', [])
            for response in responses:
                question_title = response.get('questionTitle', '')
                question_type = response.get('questionType', '')
                value = response.get('value', '')

                # Skip empty responses
                if not value or (isinstance(value, str) and not self.clean_text(value)):
                    continue
                
                # Categorize responses
                if 'rating' in question_type or 'multiple_choice' in question_type:
                    if isinstance(value, dict):
                        rating_text = f"{question_title}: {value.get('label', '')} ({value.get('value', '')})"
                    else:
                        rating_text = f"{question_title}: {value}"
                    response_groups['performance_ratings'].append(rating_text)
                
                elif 'paragraph_text' in question_type or 'text' in question_type:
                    if 'Business Unit' in question_title or 'business unit' in question_title.lower():
                        if isinstance(value, dict):
                            business_text = f"{question_title}: {value.get('label', '')}"
                        else:
                            business_text = f"{question_title}: {value}"
                        response_groups['business_context'].append(business_text)
                    elif 'project' in question_title.lower() or 'initiative' in question_title.lower():
                        response_groups['projects_initiatives'].append(f"{question_title}: {value}")
                    else:
                        response_groups['text_feedback'].append(f"{question_title}: {value}")
        
        # Create chunks for each category
        for category, responses_list in response_groups.items():
            if responses_list:
                content = f"""
                Participant: {participant_name}
                Supplier: {supplier_name}
                Survey Period: {survey_period}
                Category: {category.replace('_', ' ').title()}
                
                """ + "\n\n".join(responses_list)
                
                # Create unique chunk ID with counter
                self.chunk_counter += 1
                chunk_id = f"chunk_{self.chunk_counter}_{supplier_name}_{participant_name}_{survey_period}_{category}"

                chunk = SurveyChunk(
                    chunk_id=chunk_id,
                    content=self.clean_text(content),
                    metadata={
                        'supplier_name': supplier_name,
                        'participant_name': participant_name,
                        'project_name': project_name,
                        'survey_period': survey_period,
                        'response_category': category,
                        'chunk_type': 'participant_response'
                    },
                    chunk_type='participant_response'
                )
                chunks.append(chunk)
        
        return chunks
    
    def process_survey_data(self, data: Dict[str, Any]) -> List[SurveyChunk]:
        """Process complete survey data structure"""
        chunks = []

        # Extract client information
        client_info = data.get('client', {})

        # Process projects
        for project in client_info.get('projects', []):
            project_name = project.get('title', 'Unknown Project')

            # Process surveys within projects
            for survey in project.get('surveys', []):

                # Process evaluation periods
                for period in survey.get('evaluationPeriods', []):
                    period_title = period.get('title', 'Unknown Period')

                    # Process enabled suppliers
                    for supplier in period.get('enabledSuppliers', []):
                        supplier_name = supplier.get('name', 'Unknown Supplier')

                        # Extract supplier overview with attribute values
                        overview_chunks = self.extract_supplier_overview_with_attributes(
                            supplier, project_name, period_title
                        )
                        chunks.extend(overview_chunks)

                        # Extract participant responses
                        for participant in supplier.get('participants', []):
                            participant_chunks = self.extract_participant_responses(
                                participant, supplier_name, project_name, period_title
                            )
                            chunks.extend(participant_chunks)

        return chunks
    
    def process_files(self, file_paths: List[str]) -> List[SurveyChunk]:
        """Process multiple JSON files and return all chunks"""
        all_chunks = []
        
        for file_path in file_paths:
            print(f"Processing {file_path}...")
            data = self.load_json_file(file_path)
            if data:
                chunks = self.process_survey_data(data)
                all_chunks.extend(chunks)
                print(f"Extracted {len(chunks)} chunks from {file_path}")
        
        self.chunks = all_chunks
        return all_chunks
    
    def get_chunks_dataframe(self) -> pd.DataFrame:
        """Convert chunks to pandas DataFrame for analysis"""
        if not self.chunks:
            return pd.DataFrame()
        
        data = []
        for chunk in self.chunks:
            row = {
                'chunk_id': chunk.chunk_id,
                'content': chunk.content,
                'chunk_type': chunk.chunk_type,
                'supplier_name': chunk.metadata.get('supplier_name', ''),
                'participant_name': chunk.metadata.get('participant_name', ''),
                'project_name': chunk.metadata.get('project_name', ''),
                'survey_period': chunk.metadata.get('survey_period', ''),
                'response_category': chunk.metadata.get('response_category', ''),
                'content_length': len(chunk.content)
            }
            data.append(row)
        
        return pd.DataFrame(data)
    
    def save_chunks_to_json(self, output_path: str):
        """Save processed chunks to JSON file"""
        chunks_data = []
        for chunk in self.chunks:
            chunks_data.append({
                'chunk_id': chunk.chunk_id,
                'content': chunk.content,
                'metadata': chunk.metadata,
                'chunk_type': chunk.chunk_type
            })
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(chunks_data, f, indent=2, ensure_ascii=False)
        
        print(f"Saved {len(chunks_data)} chunks to {output_path}")


if __name__ == "__main__":
    # Example usage
    processor = SupplierDataProcessor()
    
    # Process sample files
    file_paths = ['sample1.json']  # Start with sample1, add sample2 if needed
    chunks = processor.process_files(file_paths)
    
    # Get summary statistics
    df = processor.get_chunks_dataframe()
    print("\nProcessing Summary:")
    print(f"Total chunks: {len(chunks)}")

    if not df.empty:
        print(f"Chunk types: {df['chunk_type'].value_counts().to_dict()}")
        print(f"Suppliers: {df['supplier_name'].nunique()}")
        print(f"Average content length: {df['content_length'].mean():.0f} characters")

        # Save processed chunks
        processor.save_chunks_to_json('processed_chunks.json')
    else:
        print("No chunks were extracted. Please check the data structure.")
